const express = require('express');

const cors = require('cors');
const compression = require('compression');

const cookieParser = require('cookie-parser');

const coreAuthRouter = require('./routes/coreRoutes/coreAuth');
const coreApiRouter = require('./routes/coreRoutes/coreApi');
const coreDownloadRouter = require('./routes/coreRoutes/coreDownloadRouter');
const corePublicRouter = require('./routes/coreRoutes/corePublicRouter');
const adminAuth = require('./controllers/coreControllers/adminAuth');

const errorHandlers = require('./handlers/errorHandlers');
const erpApiRouter = require('./routes/appRoutes/appApi');
const aiApiRouter = require('./routes/appRoutes/aiRoutes');
const enhancedAIApiRouter = require('./routes/appRoutes/enhancedAIRoutes');
const emailIntelligenceApiRouter = require('./routes/appRoutes/emailIntelligenceRoutes');
const invoiceAnalysisApiRouter = require('./routes/appRoutes/invoiceAnalysisRoutes');
const customerInteractionsApiRouter = require('./routes/appRoutes/customerInteractionsRoutes');
const calendarApiRouter = require('./routes/appRoutes/calendarRoutes');
const emailProcessingApiRouter = require('./routes/appRoutes/emailProcessingRoutes');
const enhancedAnalyticsApiRouter = require('./routes/appRoutes/enhancedAnalyticsRoutes');
const dataIngestionApiRouter = require('./routes/appRoutes/dataIngestionRoutes');
const aiInsightsApiRouter = require('./routes/appRoutes/aiInsightsRoutes');

const fileUpload = require('express-fileupload');
// create our Express app
const app = express();

app.use(
  cors({
    origin: true,
    credentials: true,
  })
);

app.use(cookieParser());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.use(compression());

// // default options
// app.use(fileUpload());

// Here our API Routes

app.use('/api', coreAuthRouter);
app.use('/api', adminAuth.isValidAuthToken, coreApiRouter);
app.use('/api', adminAuth.isValidAuthToken, erpApiRouter);
app.use('/api/ai', adminAuth.isValidAuthToken, aiApiRouter);
app.use('/api/ai/enhanced', adminAuth.isValidAuthToken, enhancedAIApiRouter);
app.use('/api/email-intelligence', adminAuth.isValidAuthToken, emailIntelligenceApiRouter);
app.use('/api/invoice-analysis', adminAuth.isValidAuthToken, invoiceAnalysisApiRouter);
app.use('/api/customer-interactions', adminAuth.isValidAuthToken, customerInteractionsApiRouter);
app.use('/api/calendar', adminAuth.isValidAuthToken, calendarApiRouter);
app.use('/api/email-processing', adminAuth.isValidAuthToken, emailProcessingApiRouter);
app.use('/api/analytics', adminAuth.isValidAuthToken, enhancedAnalyticsApiRouter);
app.use('/api/data-ingestion', adminAuth.isValidAuthToken, dataIngestionApiRouter);
app.use('/api/ai-insights', adminAuth.isValidAuthToken, aiInsightsApiRouter);
app.use('/download', coreDownloadRouter);
app.use('/public', corePublicRouter);

// If that above routes didnt work, we 404 them and forward to error handler
app.use(errorHandlers.notFound);

// production error handler
app.use(errorHandlers.productionErrors);

// done! we export it so we can start the site in start.js
module.exports = app;
